import Overview from "@/pages/Agent/Overview";
import type { ISidebarItem } from "@/types";

export const agentSidebarItems: ISidebarItem[] = [
  {
    title: "User Dashboard",
    items: [
      {
        title: "Overview",
        url: "/agent/overview",
        component: Overview,
      },
    ],
  },
  {
    title: "Wallet",
    items: [
      {
        title: "My Wallet",
        url: "/agent/wallet",
        component: Overview,
      },
    ],
  },
  {
    title: "Transactions",
    items: [
      {
        title: "Add money",
        url: "/agent/cash-in",
        component: Overview,
      },
      {
        title: "Withdraw money",
        url: "/agent/cash-out",
        component: Overview,
      },
      {
        title: "Agent Transactions History",
        url: "/agent/agent-transaction-history",
        component: Overview,
      },
      {
        title: "Commission history",
        url: "/agent/commission-history",
        component: Overview,
      },
    ],
  },
  {
    title: "Settings",
    items: [
      {
        title: "Profile Settings",
        url: "/agent/profile",
        component: Overview,
      },
      {
        title: "Change Password",
        url: "/agent/change-password",
        component: Overview,
      },
    ],
  },
];

import Overview from "@/pages/Agent/Overview";
import type { ISidebarItem } from "@/types";

export const adminSidebarItems: ISidebarItem[] = [
  {
    title: "Admin Dashboard",
    items: [
      {
        title: "Overview",
        url: "/admin/overview",
        component: Overview,
      },
    ],
  },
  {
    title: "Wallet",
    items: [
      {
        title: "My Wallet",
        url: "/admin/wallet",
        component: Overview,
      },
    ],
  },
  {
    title: "Transactions",
    items: [
      {
        title: "Add money",
        url: "/admin/cash-in",
        component: Overview,
      },
      {
        title: "Withdraw money",
        url: "/admin/cash-out",
        component: Overview,
      },
      {
        title: "Agent Transactions History",
        url: "/admin/agent-transaction-history",
        component: Overview,
      },
      {
        title: "Commission history",
        url: "/admin/commission-history",
        component: Overview,
      },
    ],
  },
  {
    title: "Settings",
    items: [
      {
        title: "Profile Settings",
        url: "/admin/profile",
        component: Overview,
      },
      {
        title: "Change Password",
        url: "/admin/change-password",
        component: Overview,
      },
    ],
  },
];

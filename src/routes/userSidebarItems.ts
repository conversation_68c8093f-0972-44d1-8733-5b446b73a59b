import Overview from "@/pages/User/Overview";
import type { ISidebarItem } from "@/types";

export const userSidebarItems: ISidebarItem[] = [
  {
    title: "Agent Dashboard",
    items: [
      {
        title: "Overview",
        url: "/user/overview",
        component: Overview,
      },
    ],
  },
  {
    title: "Wallet",
    items: [
      {
        title: "My Wallet",
        url: "/user/wallet",
        component: Overview,
      },
      {
        title: "Apply for Agent",
        url: "/user/apply-agent",
        component: Overview,
      },
    ],
  },
  {
    title: "Transactions",
    items: [
      {
        title: "Send Money",
        url: "/user/send-money",
        component: Overview,
      },
      {
        title: "Deposit Money",
        url: "/user/cash-in",
        component: Overview,
      },
      {
        title: "Withdraw Money",
        url: "/user/cash-out",
        component: Overview,
      },
      {
        title: "Transaction History",
        url: "/user/transaction-history",
        component: Overview,
      },
    ],
  },
  {
    title: "Settings",
    items: [
      {
        title: "Profile Settings",
        url: "/user/profile",
        component: Overview,
      },
      {
        title: "Change Password",
        url: "/user/change-password",
        component: Overview,
      },
    ],
  },
];

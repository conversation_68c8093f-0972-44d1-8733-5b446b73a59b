import { useUserInfoQuery } from "@/redux/features/auth/auth.api";
import { Navigate } from "react-router";
import type { TRole } from "../types";

export const withAuth = (
  Component: React.ComponentType,
  requiredRole?: TRole,
  superRole?: "SUPER_ADMIN"
) => {
  return function AuthWrapper() {
    const { data: user, isLoading } = useUserInfoQuery();

    if (!isLoading && !user?.data?.email) {
      return <Navigate to="/login" />;
    }
    if (
      (requiredRole && !isLoading && user?.data?.role !== requiredRole) ||
      (superRole && !isLoading && user?.data?.role !== superRole)
    ) {
      return <Navigate to="/unauthorized" />;
    }
    // else if (superRole && !isLoading && user?.data?.role !== superRole) {
    //   return <Navigate to="/unauthorized" />;
    // }
    return <Component />;
  };
};
